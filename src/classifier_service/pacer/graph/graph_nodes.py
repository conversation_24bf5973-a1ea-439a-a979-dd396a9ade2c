from langgraph.graph import StateGraph

from src.classifier_service.common.graph_state import DocumentState
from src.classifier_service.pacer.graph.graph_edges import rule_based_router_pacer, \
    initial_classification_routing_pacer, add_final_node_routing_to_correction, pacer_final_classifier_nodes
from src.classifier_service.pacer.graph.nodes_registry import initial_type_classifier_pacer, \
    notice_type_classifier_pacer, motion_type_classifier_pacer, discovery_type_classifier_pacer, \
    orders_case_management_classifier_pacer, trial_pretrial_classifier_pacer, pacer_correction_node_node
from src.classifier_service.pacer.rule_based_classification import rule_based_classifier_pacer

builder = StateGraph(DocumentState)

builder.add_node("rule_based_classifier_pacer", rule_based_classifier_pacer)
builder.add_node("initial_type_classifier_pacer", initial_type_classifier_pacer)
builder.add_node("notice_type_classifier_pacer", notice_type_classifier_pacer)
builder.add_node("motion_type_classifier_pacer", motion_type_classifier_pacer)
builder.add_node("discovery_type_classifier_pacer", discovery_type_classifier_pacer)
builder.add_node("orders_case_management_classifier_pacer", orders_case_management_classifier_pacer)
builder.add_node("trial_pretrial_classifier_pacer", trial_pretrial_classifier_pacer)

builder.add_node("self_correction", pacer_correction_node_node)

# Add edges
builder.add_conditional_edges("rule_based_classifier_pacer", rule_based_router_pacer)
builder.add_conditional_edges("initial_type_classifier_pacer", initial_classification_routing_pacer)

add_final_node_routing_to_correction(builder, pacer_final_classifier_nodes)

# Set entry and finish points
builder.set_entry_point("rule_based_classifier_pacer")
builder.set_finish_point("self_correction")

pacer_document_classifier_graph = builder.compile()


# Sample Usage
# initial_state = DocumentState(
#         document="//Users/<USER>/Desktop/fileflow/fileflow.document-classifier/tests_classifier/Doc Types Test/23.06.09 Defs Motion for Enlargement of Time to Respond to Plt Initial Discovery.pdf", # Pass the file path here
#         doc_title="23.06.09 Defs Motion for Enlargement of Time to Respond to Plt Initial Discovery.pdf",
#         classification_result=None
#     )
#
# final_result_state = pacer_document_classifier_graph.invoke(initial_state)
# print(f"Final State: {final_result_state}")
