from langgraph.graph import StateGraph

from src.classifier_service.non_court_docs.consts.case_claim import CaseClaimSubType
from src.classifier_service.non_court_docs.consts.financial import FinancialTypes
from src.classifier_service.non_court_docs.consts.general_classification import GeneralTypeClassififcation
from src.classifier_service.non_court_docs.consts.insurance import InsuranceSubType
from src.classifier_service.non_court_docs.consts.medical import HealthMedicalSubType

# TODO add fallback strategy
def rule_based_router(state) -> str:
    classification = state.get("classification_result", "").lower()

    if classification == "other_classification":
        print("[ROUTER] No rule-based classification matched, routing to GENERAL DOC TYPE CLASSIFICATION node.")
        return "general_type_classification"
    elif classification == "notice":
        return "notice_classifier"
    elif classification == 'bill':
        return 'self_correction'
    else:
        print(f"[ROUTER] Rule-based classification matched: {state['classification_result']}\n Ending the graph")
        return "__end__"


def route_document_by_general_type(state) -> str:
    general_type_str = state["classification_result"]
    try:
        general_type = GeneralTypeClassififcation(general_type_str)
    except ValueError:
        print(f"[ROUTER] Invalid general type: {general_type_str}")
        return "UnknownTypeHandler"

    print(f"[ROUTER] classification_result: {general_type}")

    if general_type == GeneralTypeClassififcation.FINANCIAL_INVESTMENT:
        return "financial_documents_classification"
    elif general_type == GeneralTypeClassififcation.INSURANCE:
        return "insurance_documents_classification"
    elif general_type == GeneralTypeClassififcation.HEALTH_MEDICAL:
        return "medical_type_classifier"
    elif general_type == GeneralTypeClassififcation.GOVERNMENT_AGENCY:
        return "government_agency_classifier"
    elif general_type == GeneralTypeClassififcation.CASE_CLAIM_MANAGEMENT:
        return "case_claim_management_classifier"
    elif general_type == GeneralTypeClassififcation.GENERAL_COMMUNICATION_ADMINISTRATIVE:
        return "general_correspondence_classifier"
    else:
        return "UnknownTypeHandler"

# Financial documents router
def financial_type_router(state) -> str:

    financial_type = state["classification_result"]

    if financial_type == FinancialTypes.BANKING_AND_ACCOUNTS.value:
        return "banking_and_accounting_doc_type"
    elif financial_type == FinancialTypes.LOANS_AND_CREDIT.value:
        return "loans_and_credits_doc_type"
    elif financial_type == FinancialTypes.INVESTMENTS_AND_RETIREMENT.value:
        return "investment_and_retirement_doc_type"
    elif financial_type == FinancialTypes.INCOME_AND_BENEFITS.value:
        return "income_and_benefits_doc_type"
    elif financial_type == FinancialTypes.BILLING_INVOICES_PAYMENTS.value:
        return "billing_invoices_payments_doc_type"
    elif financial_type == FinancialTypes.PROPERTY_AND_ASSET_MANAGEMENT.value:
        return "property_and_asset_management_doc_type"
    else:
        return "unknown_financial_type_handler"

# TODO - test combining in one node
# def insurance_type_router(state) -> str:
#     insurance_type = state["classification_result"]
#
#     if insurance_type == InsuranceSubType.POLICY_DETAILS_AND_COVERAGE.value:
#         return "policy_details_and_coverage_type"
#     elif insurance_type == InsuranceSubType.CLAIMS_PROCESS_AND_COMMUNICATION.value:
#         return "claims_process_and_communication_type"
#     else:
#         return "unknown_insurance_type_handler"


def medical_type_router(state):
    medical_type = state["classification_result"]
    if medical_type == HealthMedicalSubType.GOVERNMENT_BENEFIT_ADMINISTRATION.value:
        return "gov_benefit_administration_classifier"
    elif medical_type == HealthMedicalSubType.MEDICAL_RECORDS_AND_EVALUATIONS.value:
        return "medical_records_evaluation_classifier"
    elif medical_type == HealthMedicalSubType.HEALTHCARE_FINANCE_AND_CLAIMS.value:
        return "healthcare_finance_claims_classifier"


def case_management_router(state):
    case_management_type = state["classification_result"]

    if case_management_type == CaseClaimSubType.CLIENT_INTAKE_AND_PROFILE.value:
        return "client_profile_classifier"
    elif case_management_type == CaseClaimSubType.CASE_MANAGEMENT_AND_WORKFLOW.value:
        return "case_management_classifier"
    elif case_management_type == CaseClaimSubType.CLAIMS_AND_RESOLUTIONS.value:
        return "case_resolution_classifier"
    elif case_management_type == CaseClaimSubType.FORMAL_LEGAL_AND_ADMIN.value:
        return "formal_legal_admin_classifier"

    return "unknown_case_management_handler"

def add_final_node_routing_to_correction(builder: StateGraph, final_nodes: list[str]):
    """
    Adds a routing edge from each final node to the 'self_correction' node.

    Args:
        builder: The StateGraph builder instance.
        final_nodes: List of node names that are leaf nodes (typically routed to __end__).
    """
    for node_name in final_nodes:
        builder.add_conditional_edges(node_name, lambda state: "self_correction")