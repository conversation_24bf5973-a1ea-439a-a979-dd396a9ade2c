
# Initial Classification Prompt
def base_non_court_classification_prompt(options: list[str], category_name: str | None = None, category_descriptions =None) -> str:
    options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a document classification assistant.

{header}Please classify the document into exactly ONE of the following types:

{options_str}
Here is additional information about all potential categories that might help you to decide:
{category_descriptions}

Respond ONLY with the document type, exactly as listed above.
"""
    return prompt.strip()


def non_court_self_correction_prompt(options: list[str], category_name: str | None = None) -> str:
    # options_str = "\n".join(f"- {opt}" for opt in options)

    header = (
        f'The document has been identified as belonging to the "{category_name}" category.\n\n'
        if category_name else ""
    )

    prompt = f"""
You are a legal document classification assistant.

{header} Your task is to verify is the document has been correctly classified into this category.

Steps to Verify:

1. Identify document title
2. Analyze title - does it confirm that the document belongs to the category "{category_name}"?
3. Pay attention not only to the word but to the meaning of the title - does it confirm the category "{category_name}"?
4. Look into the whole document structure and information - doe sit confirm the category "{category_name}"?

Follow these rules:

1. If a word BILL appears in the document title or text determine whether it is a Billing Records or Medical Billing, Billing Request
2. Offer Letter: Whenever you see a letter from insurance company is offering money to settle, always classify it as Offer Letter.
3. if a word INVOICE there are high chances it might be an Invoice, check carefully
4. If it has been classified as NOTICE look into all available Notice Types to determine the exact type of notice. If it doesn't fit any of the specific notice types, classify it as NOTICE.

If YES, return the document category as it is.

If you believe the document has been misclassified, return the correct category from the list below and explainn why you think previous classification is incorrect. If you are 100% sure that correct category is not in the list below, return the category that you believe it belongs to, even if it is not in the lista nd explain why in explanation section:

{options}

"""
    return prompt.strip()