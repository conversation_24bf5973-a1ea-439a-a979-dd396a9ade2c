from src.classifier_service.common.config import gemini_client_classifier
from src.classifier_service.efile.prompts.prompt_factory import legal_category_prompt
from src.shared.service_registry import gemini_client

# file_path = "some legal text here"
# result = gemini_client.generate_from_text(legal_category_prompt, file_path)
# print(result)


# file_path = "What types of legal documents are there?"
# result = gemini_client_classifier.generate_from_text(legal_category_prompt, file_path)
# print(result)