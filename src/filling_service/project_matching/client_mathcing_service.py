import json
import sys
import os

# Add the project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.insert(0, project_root)

from src.filling_service.project_matching.utils import extract_plain_text_from_email_body
from src.shared.service_registry import doc_processor


def match_project(doc_id):
    """
    Extract and process email metadata for project matching.

    Args:
        doc_id (int): The document ID to process

    Returns:
        dict: Dictionary containing extracted email metadata including subject,
              plain text content, sender info, and recipients
    """
    # Get email metadata
    email_info = doc_processor.get_document_info(doc_id)
    if not email_info:
        raise ValueError("Failed to retrieve document metadata.")

    # email_info is already a dictionary, no need to parse JSON
    metadata = email_info.get("metadata", {})
    subject = metadata.get("subject", "")

    # Extract plain text from email body using the email_info dictionary directly
    plain_text = extract_plain_text_from_email_body(email_info)

    sender = metadata.get("sender", "")
    sent_from = metadata.get("from", "")
    to_recipients = metadata.get("to_recipients", "")
    cc_recipients = metadata.get("cc_recipients", "")

    print(f"Email Metadata --- Subject: {subject},\n\n Email Text: {plain_text},\n\n Sender: {sender}\n'n, Sent From: {sent_from}\n\n, To Recipients: {to_recipients}\n\n, CC Recipients: {cc_recipients}")

    return {
        'subject': subject,
        'plain_text': plain_text,
        'sender': sender,
        'sent_from': sent_from,
        'to_recipients': to_recipients,
        'cc_recipients': cc_recipients,
        'metadata': metadata
    }


if __name__ == "__main__":
    # Test the function with document ID 268
    result = match_project(268)
    print("Function completed successfully!")







