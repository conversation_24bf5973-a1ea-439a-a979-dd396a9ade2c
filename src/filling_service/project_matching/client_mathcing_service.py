from src.shared.service_registry import doc_processor


def match_project(doc_id):

    # Get email metadata
    email_info = doc_processor.get_document_info(doc_id)
    if not email_info:
        raise ValueError("Failed to retrieve document metadata.")
    metadata = email_info.get("metadata", {})
    subject = metadata.get("subject", "")
    email_text = metadata.get("email_body", "")
    sender = metadata.get("sender", "")
    sent_from = metadata.get("from", "")
    to_recipients = metadata.get("to_recipients", "")
    cc_recipients = metadata.get("cc_recipients", "")

    print(f"Email Metadata --- Subject: {subject}, Email Text: {email_text}, Sender: {sender}, Sent From: {sent_from}, To Recipients: {to_recipients}, CC Recipients: {cc_recipients}")


match_project(268)







