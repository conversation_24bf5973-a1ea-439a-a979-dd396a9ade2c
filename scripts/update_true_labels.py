#!/usr/bin/env python3
"""
<PERSON>ript to update true labels in classification results file by matching PDF titles
with a labeled dataset file.

Usage:
    python update_true_labels.py <classification_results_file> <labeled_dataset_file> [output_file]

Arguments:
    classification_results_file: Excel file with classification results (must have 'PDF Title' column)
    labeled_dataset_file: Excel file with true labels (must have 'PDF Title' and 'True Label' columns)
    output_file: Optional output file path. If not provided, will overwrite the classification results file.

Example:
    python update_true_labels.py results.xlsx labeled_data.xlsx updated_results.xlsx
"""

import pandas as pd
import sys
import os
from pathlib import Path


def update_true_labels(classification_file, labeled_file, output_file=None):
    """
    Update true labels in classification results by matching PDF titles.
    
    Args:
        classification_file (str): Path to classification results Excel file
        labeled_file (str): Path to labeled dataset Excel file
        output_file (str, optional): Path to output file. If None, overwrites classification_file
    
    Returns:
        tuple: (total_rows, matched_rows, updated_file_path)
    """
    
    # Read the classification results file
    print(f"Reading classification results from: {classification_file}")
    try:
        df_results = pd.read_excel(classification_file)
    except Exception as e:
        raise Exception(f"Error reading classification results file: {e}")
    
    # Validate required columns in results file
    if 'PDF Title' not in df_results.columns:
        raise Exception("Classification results file must have 'PDF Title' column")
    
    print(f"Found {len(df_results)} rows in classification results")
    
    # Read the labeled dataset file
    print(f"Reading labeled dataset from: {labeled_file}")
    try:
        df_labeled = pd.read_excel(labeled_file)
    except Exception as e:
        raise Exception(f"Error reading labeled dataset file: {e}")
    
    # Validate required columns in labeled file
    required_cols = ['PDF Title', 'True Label']
    missing_cols = [col for col in required_cols if col not in df_labeled.columns]
    if missing_cols:
        raise Exception(f"Labeled dataset file must have columns: {missing_cols}")
    
    print(f"Found {len(df_labeled)} rows in labeled dataset")
    
    # Create a mapping dictionary from PDF Title to True Label
    label_mapping = dict(zip(df_labeled['PDF Title'], df_labeled['True Label']))
    
    # Add or update the 'Actual Doc Type' column with true labels
    matched_count = 0
    for idx, row in df_results.iterrows():
        pdf_title = row['PDF Title']
        if pdf_title in label_mapping:
            df_results.at[idx, 'Actual Doc Type'] = label_mapping[pdf_title]
            matched_count += 1
    
    # Determine output file path
    if output_file is None:
        output_file = classification_file
    
    # Save the updated results
    print(f"Saving updated results to: {output_file}")
    try:
        df_results.to_excel(output_file, index=False)
    except Exception as e:
        raise Exception(f"Error saving updated file: {e}")
    
    return len(df_results), matched_count, output_file


def main():
    """Main function to handle command line arguments and execute the update."""
    
    if len(sys.argv) < 3:
        print(__doc__)
        sys.exit(1)
    
    classification_file = sys.argv[1]
    labeled_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    # Validate input files exist
    if not os.path.exists(classification_file):
        print(f"Error: Classification results file not found: {classification_file}")
        sys.exit(1)
    
    if not os.path.exists(labeled_file):
        print(f"Error: Labeled dataset file not found: {labeled_file}")
        sys.exit(1)
    
    try:
        total_rows, matched_rows, output_path = update_true_labels(
            classification_file, labeled_file, output_file
        )
        
        print("\n" + "="*60)
        print("UPDATE SUMMARY")
        print("="*60)
        print(f"Total rows in classification results: {total_rows}")
        print(f"Rows with matching PDF titles: {matched_rows}")
        print(f"Rows without matches: {total_rows - matched_rows}")
        print(f"Match rate: {matched_rows/total_rows*100:.1f}%")
        print(f"Updated file saved to: {output_path}")
        print("="*60)
        
        if matched_rows < total_rows:
            print(f"\nNote: {total_rows - matched_rows} rows could not be matched.")
            print("This could be due to:")
            print("- PDF titles not present in the labeled dataset")
            print("- Slight differences in PDF title formatting")
            print("- Case sensitivity differences")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
